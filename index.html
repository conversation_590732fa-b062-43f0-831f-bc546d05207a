<!DOCTYPE html>
<html lang="ru">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>LART</title>
    <link rel="stylesheet" href="style.css">
</head>
<body>
    <!-- SVG фильтр для зернистости -->
    <svg style="display: none;">
        <filter id="noiseFilter">
            <feTurbulence
                type="fractalNoise"
                baseFrequency="0.9"
                numOctaves="4"
                stitchTiles="stitch"/>
        </filter>
        <rect width="100%" height="100%" filter="url(#noiseFilter)"/>
    </svg>

    <div class="container">
        <div class="background-rectangle">
            <div class="noise-layer"></div>
            <div class="glitch"></div>
            <h1 class="title">LART</h1>
        </div>
    </div>
</body>
</html>
