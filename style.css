* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    height: 100vh;
    background: #000;
    display: flex;
    justify-content: center;
    align-items: center;
    font-family: 'Arial', sans-serif;
    overflow: hidden;
}

.container {
    width: 100%;
    height: 100%;
    display: flex;
    justify-content: center;
    align-items: center;
}

.background-rectangle {
    width: 90vw;
    height: 70vh;
    max-width: 1200px;
    max-height: 800px;
    border-radius: 20px;
    background: linear-gradient(45deg,
        #ff6b6b, #4ecdc4, #45b7d1, #96ceb4,
        #feca57, #ff9ff3, #54a0ff, #5f27cd);
    background-size: 400% 400%;
    animation: gradientShift 8s ease infinite;
    position: relative;
    display: flex;
    justify-content: center;
    align-items: center;
    overflow: hidden;
    box-shadow: 0 0 50px rgba(255, 255, 255, 0.1);
    isolation: isolate;
}

.noise-layer {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background:
        linear-gradient(transparent, transparent),
        url('./noise.svg');
    filter: contrast(170%) brightness(1000%);
    opacity: 0.5;
    mix-blend-mode: overlay;
    border-radius: 20px;
    animation: noiseAnimation 4s linear infinite;
}

@keyframes noiseAnimation {
    0% { transform: translate(0, 0) scale(1); }
    25% { transform: translate(-1px, -1px) scale(1.01); }
    50% { transform: translate(1px, 1px) scale(0.99); }
    75% { transform: translate(-1px, 1px) scale(1.01); }
    100% { transform: translate(0, 0) scale(1); }
}

.glitch {
    top: -320vw;
    right: -320vw;
    bottom: -320vw;
    left: -320vw;
    width: auto;
    height: auto;
    position: absolute;
    z-index: 2;
    background-position: 50%;
    background-image: url('https://www.bleedblack.co/wp-content/uploads/2021/09/grind.png');
    background-size: cover;
    opacity: 0.4;
    pointer-events: none;
    animation: glitchEffect 2s steps(2) infinite;
    border-radius: 20px;
}

.title {
    font-size: 8vmin;
    font-weight: 900;
    color: #fff;
    text-shadow: 
        0 0 20px rgba(255, 255, 255, 0.8),
        0 0 40px rgba(255, 255, 255, 0.6),
        0 0 60px rgba(255, 255, 255, 0.4);
    letter-spacing: 0.5em;
    z-index: 3;
    position: relative;
    animation: textGlow 3s ease-in-out infinite alternate;
}

@keyframes gradientShift {
    0% {
        background-position: 0% 50%;
    }
    50% {
        background-position: 100% 50%;
    }
    100% {
        background-position: 0% 50%;
    }
}

@keyframes glitchEffect {
    0% { transform: translate3d(0, 9rem, 0); }
    10% { transform: translate3d(-1rem, -4rem, 0); }
    20% { transform: translate3d(-8rem, 2rem, 0); }
    30% { transform: translate3d(9rem, -9rem, 0); }
    40% { transform: translate3d(-2rem, 7rem, 0); }
    50% { transform: translate3d(-9rem, -4rem, 0); }
    60% { transform: translate3d(2rem, 6rem, 0); }
    70% { transform: translate3d(7rem, -8rem, 0); }
    80% { transform: translate3d(-9rem, 1rem, 0); }
    90% { transform: translate3d(6rem, -5rem, 0); }
    100% { transform: translate3d(-7rem, 0, 0); }
}



@keyframes textGlow {
    0% {
        text-shadow: 
            0 0 20px rgba(255, 255, 255, 0.8),
            0 0 40px rgba(255, 255, 255, 0.6),
            0 0 60px rgba(255, 255, 255, 0.4);
    }
    100% {
        text-shadow: 
            0 0 30px rgba(255, 255, 255, 1),
            0 0 60px rgba(255, 255, 255, 0.8),
            0 0 90px rgba(255, 255, 255, 0.6);
    }
}

/* Адаптивность для мобильных устройств */
@media (max-width: 768px) {
    .background-rectangle {
        width: 95vw;
        height: 60vh;
    }

    .title {
        font-size: 10vmin;
    }
}

@media (max-width: 480px) {
    .background-rectangle {
        width: 98vw;
        height: 50vh;
        border-radius: 15px;
    }

    .title {
        font-size: 12vmin;
        letter-spacing: 0.3em;
    }
}
