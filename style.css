* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    height: 100vh;
    background: #000;
    display: flex;
    justify-content: center;
    align-items: center;
    font-family: 'Arial', sans-serif;
    overflow: hidden;
}

.container {
    width: 100%;
    height: 100%;
    display: flex;
    justify-content: center;
    align-items: center;
}

.background-square {
    width: 80vmin;
    height: 80vmin;
    border-radius: 20px;
    background: linear-gradient(45deg, 
        #ff6b6b, #4ecdc4, #45b7d1, #96ceb4, 
        #feca57, #ff9ff3, #54a0ff, #5f27cd);
    background-size: 400% 400%;
    animation: gradientShift 8s ease infinite;
    position: relative;
    display: flex;
    justify-content: center;
    align-items: center;
    overflow: hidden;
    box-shadow: 0 0 50px rgba(255, 255, 255, 0.1);
}

.background-square::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-image: 
        radial-gradient(circle at 25% 25%, rgba(255, 255, 255, 0.3) 1px, transparent 1px),
        radial-gradient(circle at 75% 75%, rgba(255, 255, 255, 0.2) 1px, transparent 1px),
        radial-gradient(circle at 50% 50%, rgba(255, 255, 255, 0.1) 1px, transparent 1px);
    background-size: 4px 4px, 6px 6px, 8px 8px;
    opacity: 0.6;
    animation: noiseMove 3s linear infinite;
}

.glitch {
    top: -50%;
    right: -50%;
    bottom: -50%;
    left: -50%;
    width: auto;
    height: auto;
    position: absolute;
    z-index: 2;
    background-position: 50%;
    background-image: url('https://www.bleedblack.co/wp-content/uploads/2021/09/grind.png');
    background-size: cover;
    opacity: 0.3;
    pointer-events: none;
    animation: glitchEffect 2s steps(2) infinite;
    border-radius: 20px;
}

.title {
    font-size: 8vmin;
    font-weight: 900;
    color: #fff;
    text-shadow: 
        0 0 20px rgba(255, 255, 255, 0.8),
        0 0 40px rgba(255, 255, 255, 0.6),
        0 0 60px rgba(255, 255, 255, 0.4);
    letter-spacing: 0.5em;
    z-index: 3;
    position: relative;
    animation: textGlow 3s ease-in-out infinite alternate;
}

@keyframes gradientShift {
    0% {
        background-position: 0% 50%;
    }
    50% {
        background-position: 100% 50%;
    }
    100% {
        background-position: 0% 50%;
    }
}

@keyframes glitchEffect {
    0% { transform: translate3d(0, 9rem, 0); }
    10% { transform: translate3d(-1rem, -4rem, 0); }
    20% { transform: translate3d(-8rem, 2rem, 0); }
    30% { transform: translate3d(9rem, -9rem, 0); }
    40% { transform: translate3d(-2rem, 7rem, 0); }
    50% { transform: translate3d(-9rem, -4rem, 0); }
    60% { transform: translate3d(2rem, 6rem, 0); }
    70% { transform: translate3d(7rem, -8rem, 0); }
    80% { transform: translate3d(-9rem, 1rem, 0); }
    90% { transform: translate3d(6rem, -5rem, 0); }
    100% { transform: translate3d(-7rem, 0, 0); }
}

@keyframes noiseMove {
    0% { transform: translate(0, 0); }
    25% { transform: translate(-2px, -2px); }
    50% { transform: translate(2px, 2px); }
    75% { transform: translate(-1px, 1px); }
    100% { transform: translate(0, 0); }
}

@keyframes textGlow {
    0% {
        text-shadow: 
            0 0 20px rgba(255, 255, 255, 0.8),
            0 0 40px rgba(255, 255, 255, 0.6),
            0 0 60px rgba(255, 255, 255, 0.4);
    }
    100% {
        text-shadow: 
            0 0 30px rgba(255, 255, 255, 1),
            0 0 60px rgba(255, 255, 255, 0.8),
            0 0 90px rgba(255, 255, 255, 0.6);
    }
}

/* Адаптивность для мобильных устройств */
@media (max-width: 768px) {
    .background-square {
        width: 90vmin;
        height: 90vmin;
    }
    
    .title {
        font-size: 10vmin;
    }
}
